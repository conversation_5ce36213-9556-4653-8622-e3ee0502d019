import 'dart:async';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';

import 'notification_repository.dart';
import 'notification_template_service.dart';
import 'notification_delivery_service.dart';
import '../models/notification_isar.dart';
import '../models/scheduled_notification_request.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';

/// Focused service for notification scheduling and timing operations
/// Handles scheduled notifications, recurring notifications, and timing logic
/// Single responsibility: managing notification scheduling and execution
class NotificationSchedulingService {
  final Logger _logger = Logger('NotificationSchedulingService');
  
  // Use lazy getters to avoid accessing GetIt services in constructor
  NotificationRepository get _repository => GetIt.instance<NotificationRepository>();
  NotificationTemplateService get _templateService => GetIt.instance<NotificationTemplateService>();
  NotificationDeliveryService get _deliveryService => GetIt.instance<NotificationDeliveryService>();

  // Timer for processing scheduled notifications
  Timer? _scheduledTasksTimer;
  
  // Stream controller for scheduling events
  final StreamController<String> _schedulingEventsController = StreamController<String>.broadcast();
  
  /// Stream of scheduling events
  Stream<String> get onSchedulingEvents => _schedulingEventsController.stream;

  //=== INITIALIZATION ===//

  /// Initialize the scheduling service
  Future<void> initialize() async {
    try {
      // Start background processing of scheduled notifications
      _startScheduledTasksProcessor();
      
      _logger.info('NotificationSchedulingService initialized');
    } catch (e) {
      _logger.severe('Error initializing NotificationSchedulingService: $e');
      rethrow;
    }
  }

  /// Dispose resources
  void dispose() {
    _scheduledTasksTimer?.cancel();
    _schedulingEventsController.close();
  }

  //=== SCHEDULING OPERATIONS ===//

  /// Schedule a notification for future delivery
  Future<void> scheduleNotification(ScheduledNotificationRequest request) async {
    try {
      // Validate scheduling request
      _validateSchedulingRequest(request);
      
      // Create notification from template
      final notification = _templateService.createScheduledNotificationFromRequest(request);
      
      // Save to repository
      await _repository.saveNotification(notification);
      
      // Emit scheduling event
      _schedulingEventsController.add('notification_scheduled:${notification.businessId}');
      
      _logger.info('Scheduled notification: ${notification.businessId} for ${request.scheduledFor}');
    } catch (e) {
      _logger.severe('Error scheduling notification: $e');
      rethrow;
    }
  }

  /// Cancel a scheduled notification
  Future<void> cancelScheduledNotification(String notificationId) async {
    try {
      await _repository.deleteNotification(notificationId);
      
      // Emit scheduling event
      _schedulingEventsController.add('notification_cancelled:$notificationId');
      
      _logger.info('Cancelled scheduled notification: $notificationId');
    } catch (e) {
      _logger.severe('Error cancelling scheduled notification: $e');
      rethrow;
    }
  }

  /// Reschedule a notification
  Future<void> rescheduleNotification(String notificationId, DateTime newScheduledTime) async {
    try {
      final notification = await _repository.getNotificationById(notificationId);
      if (notification == null) {
        throw Exception('Notification not found: $notificationId');
      }

      // Update scheduled time
      notification.scheduledFor = newScheduledTime;
      notification.updatedAt = DateTime.now();
      
      // Save updated notification
      await _repository.saveNotification(notification);
      
      // Emit scheduling event
      _schedulingEventsController.add('notification_rescheduled:$notificationId');
      
      _logger.info('Rescheduled notification: $notificationId to $newScheduledTime');
    } catch (e) {
      _logger.severe('Error rescheduling notification: $e');
      rethrow;
    }
  }

  //=== SCHEDULED TASKS PROCESSING ===//

  /// Start background processor for scheduled notifications
  void _startScheduledTasksProcessor() {
    // Process scheduled notifications every minute
    _scheduledTasksTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _processScheduledNotifications();
    });
    
    _logger.info('Started scheduled tasks processor');
  }

  /// Process due scheduled notifications
  Future<void> _processScheduledNotifications() async {
    try {
      final now = DateTime.now();
      
      // Get notifications that are due for delivery
      final dueNotifications = await _getDueNotifications(now);
      
      for (final notification in dueNotifications) {
        await _processScheduledNotification(notification, now);
      }
      
      if (dueNotifications.isNotEmpty) {
        _logger.info('Processed ${dueNotifications.length} scheduled notifications');
      }
    } catch (e) {
      _logger.severe('Error processing scheduled notifications: $e');
    }
  }

  /// Get notifications that are due for delivery
  Future<List<NotificationIsar>> _getDueNotifications(DateTime now) async {
    try {
      // Create filter for due notifications
      final filter = NotificationFilter(
        status: NotificationStatus.unread,
        scheduledBefore: now,
      );
      
      final notifications = await _repository.getNotifications(filter: filter);
      
      // Filter for notifications that are actually due
      return notifications.where((notification) {
        if (notification.scheduledFor == null) return false;
        if (notification.status == NotificationStatus.delivered) return false;
        if (notification.expiresAt != null && notification.expiresAt!.isBefore(now)) return false;
        
        return notification.scheduledFor!.isBefore(now) || notification.scheduledFor!.isAtSameMomentAs(now);
      }).toList();
    } catch (e) {
      _logger.severe('Error getting due notifications: $e');
      return [];
    }
  }

  /// Process a single scheduled notification
  Future<void> _processScheduledNotification(NotificationIsar notification, DateTime now) async {
    try {
      // Check if notification has expired
      if (notification.expiresAt != null && notification.expiresAt!.isBefore(now)) {
        // Mark as expired and skip delivery
        notification.status = NotificationStatus.expired;
        notification.updatedAt = now;
        await _repository.saveNotification(notification);
        
        _logger.info('Notification expired: ${notification.businessId}');
        return;
      }

      // Deliver the notification
      await _deliveryService.deliverNotification(notification);
      
      // Update notification status
      notification.status = NotificationStatus.delivered;
      notification.updatedAt = now;
      
      // Handle recurring notifications
      if (notification.isRecurring && notification.recurringPattern != null) {
        await _handleRecurringNotification(notification, now);
      }
      
      // Save updated notification
      await _repository.saveNotification(notification);
      
      // Emit scheduling event
      _schedulingEventsController.add('notification_delivered:${notification.businessId}');
      
      _logger.info('Delivered scheduled notification: ${notification.businessId}');
    } catch (e) {
      _logger.severe('Error processing scheduled notification ${notification.businessId}: $e');
    }
  }

  /// Handle recurring notification scheduling
  Future<void> _handleRecurringNotification(NotificationIsar notification, DateTime now) async {
    try {
      if (notification.recurringPattern == null) return;
      
      // Calculate next occurrence based on pattern
      final nextScheduledTime = _calculateNextOccurrence(now, notification.recurringPattern!);
      
      if (nextScheduledTime != null) {
        // Create new scheduled notification for next occurrence
        final nextNotification = NotificationIsar(
          title: notification.title,
          message: notification.message,
          category: notification.category,
          type: notification.type,
          priority: notification.priority,
          cattleId: notification.cattleId,
          relatedRecordId: notification.relatedRecordId,
          relatedRecordType: notification.relatedRecordType,
          eventId: notification.eventId,
          imageUrl: notification.imageUrl,
          actionUrl: notification.actionUrl,
          customData: notification.customData,
          scheduledFor: nextScheduledTime,
          expiresAt: notification.expiresAt,
          isRecurring: true,
          recurringPattern: notification.recurringPattern,
          createdAt: now,
          businessId: notification.businessId, // Keep same business ID for recurring series
        );
        
        await _repository.saveNotification(nextNotification);
        
        _logger.info('Scheduled next recurring notification: ${notification.businessId} for $nextScheduledTime');
      }
    } catch (e) {
      _logger.severe('Error handling recurring notification: $e');
    }
  }

  /// Calculate next occurrence for recurring notification
  DateTime? _calculateNextOccurrence(DateTime current, String pattern) {
    try {
      switch (pattern.toLowerCase()) {
        case 'daily':
          return current.add(const Duration(days: 1));
        case 'weekly':
          return current.add(const Duration(days: 7));
        case 'monthly':
          return DateTime(current.year, current.month + 1, current.day, current.hour, current.minute);
        case 'yearly':
          return DateTime(current.year + 1, current.month, current.day, current.hour, current.minute);
        default:
          _logger.warning('Unknown recurring pattern: $pattern');
          return null;
      }
    } catch (e) {
      _logger.severe('Error calculating next occurrence for pattern $pattern: $e');
      return null;
    }
  }

  //=== VALIDATION ===//

  /// Validate scheduling request
  void _validateSchedulingRequest(ScheduledNotificationRequest request) {
    if (request.scheduledFor.isBefore(DateTime.now())) {
      throw ArgumentError('Cannot schedule notification in the past');
    }
    
    if (request.expiresAt != null && request.expiresAt!.isBefore(request.scheduledFor)) {
      throw ArgumentError('Expiration time cannot be before scheduled time');
    }
    
    if (request.title.trim().isEmpty) {
      throw ArgumentError('Notification title cannot be empty');
    }
    
    if (request.message.trim().isEmpty) {
      throw ArgumentError('Notification message cannot be empty');
    }
  }

  //=== QUERY METHODS ===//

  /// Get all scheduled notifications
  Future<List<NotificationIsar>> getScheduledNotifications() async {
    try {
      final filter = NotificationFilter(
        hasScheduledTime: true,
        status: NotificationStatus.unread,
      );
      
      return await _repository.getNotifications(filter: filter);
    } catch (e) {
      _logger.severe('Error getting scheduled notifications: $e');
      return [];
    }
  }

  /// Get upcoming notifications (next 24 hours)
  Future<List<NotificationIsar>> getUpcomingNotifications() async {
    try {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));
      
      final filter = NotificationFilter(
        scheduledAfter: now,
        scheduledBefore: tomorrow,
        status: NotificationStatus.unread,
      );
      
      return await _repository.getNotifications(filter: filter);
    } catch (e) {
      _logger.severe('Error getting upcoming notifications: $e');
      return [];
    }
  }
}
