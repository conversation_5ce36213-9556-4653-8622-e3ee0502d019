import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';
import '../services/notification_repository.dart';
import '../services/notification_analytics_service.dart';

/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

/// Notifications Controller
///
/// Manages state for the notifications system following established patterns.
/// Implements dual-stream architecture with filtered/unfiltered data separation.
/// Analytics are ALWAYS calculated on unfiltered data for accuracy.
///
/// Follows the cattle/weight controller pattern with reactive streams.
class NotificationsController extends ChangeNotifier {
  // Use lazy getters to avoid accessing GetIt services in constructor
  NotificationRepository get _notificationRepository => GetIt.instance<NotificationRepository>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<NotificationIsar>>? _unfilteredNotificationSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<NotificationIsar> _unfilteredNotifications = []; // Complete dataset for analytics calculations
  List<NotificationIsar> _filteredNotifications = []; // Filtered dataset for UI display

  // Current filter settings
  NotificationFilter _currentFilter = const NotificationFilter();
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  NotificationAnalyticsResult _analyticsResult = NotificationAnalyticsResult.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<NotificationIsar> get notifications => _filteredNotifications;
  NotificationFilter get currentFilter => _currentFilter;
  bool get hasActiveFilters => _hasActiveFilters;
  NotificationAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters from analytics
  int get unreadCount => _analyticsResult.unreadCount;
  int get totalCount => _analyticsResult.totalCount;
  Map<String, int> get categoryCounts => _analyticsResult.categoryCounts;
  Map<NotificationStatus, int> get statusCounts => _analyticsResult.statusCounts;

  // Constructor
  NotificationsController() {
    _initializeStreamListeners();
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);
      // Data comes from Isar watch() streams automatically
      // State management is handled by _handleStreamUpdate
    } catch (e, stackTrace) {
      debugPrint('Error loading notifications data: $e\n$stackTrace');
      _setError('Failed to load notifications data: ${e.toString()}');
    }
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredNotificationSubscription = _notificationRepository.watchAllNotifications()
        .listen((unfilteredList) {
      _unfilteredNotifications = unfilteredList;
      _updateAnalytics();
      _applyFilters();
    });
  }

  /// Update analytics - ALWAYS calculated on unfiltered data
  void _updateAnalytics() {
    _analyticsResult = NotificationAnalyticsService.calculate(_unfilteredNotifications);
  }

  /// Apply filters to notifications - triggers database-level filtering when possible
  Future<void> applyFilters(NotificationFilter filter) async {
    try {
      _setState(ControllerState.loading);
      _currentFilter = filter;
      _hasActiveFilters = _hasFilters(filter);
      
      // Apply filters and update UI
      _applyFilters();
    } catch (e) {
      _setError('Failed to apply filters: ${e.toString()}');
    }
  }

  /// Internal method to apply current filters
  void _applyFilters() {
    try {
      if (!_hasActiveFilters) {
        // No filters - show all notifications
        _filteredNotifications = List.from(_unfilteredNotifications);
      } else {
        // Apply filters in memory for now (can be optimized with database queries later)
        _filteredNotifications = _unfilteredNotifications.where((notification) {
          // Status filter
          if (_currentFilter.status != null && notification.status != _currentFilter.status) {
            return false;
          }

          // Category filter
          if (_currentFilter.category != null && 
              _currentFilter.category!.isNotEmpty && 
              notification.category != _currentFilter.category) {
            return false;
          }

          // Cattle ID filter
          if (_currentFilter.cattleId != null && 
              _currentFilter.cattleId!.isNotEmpty && 
              notification.cattleId != _currentFilter.cattleId) {
            return false;
          }

          // Date range filter
          if (_currentFilter.fromDate != null && 
              notification.createdAt != null &&
              notification.createdAt!.isBefore(_currentFilter.fromDate!)) {
            return false;
          }

          if (_currentFilter.toDate != null && 
              notification.createdAt != null &&
              notification.createdAt!.isAfter(_currentFilter.toDate!)) {
            return false;
          }

          // Search query filter
          if (_currentFilter.searchQuery != null && 
              _currentFilter.searchQuery!.isNotEmpty) {
            final query = _currentFilter.searchQuery!.toLowerCase();
            final title = notification.title?.toLowerCase() ?? '';
            final message = notification.message?.toLowerCase() ?? '';
            
            if (!title.contains(query) && !message.contains(query)) {
              return false;
            }
          }

          return true;
        }).toList();
      }

      // Sort notifications by creation date (newest first)
      _filteredNotifications.sort((a, b) {
        final aDate = a.createdAt ?? DateTime.fromMillisecondsSinceEpoch(0);
        final bDate = b.createdAt ?? DateTime.fromMillisecondsSinceEpoch(0);
        return bDate.compareTo(aDate);
      });

      _setState(ControllerState.loaded);
    } catch (e) {
      _setError('Failed to apply filters: ${e.toString()}');
    }
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await applyFilters(const NotificationFilter());
  }

  /// Check if filter has active filters
  bool _hasFilters(NotificationFilter filter) {
    return filter.status != null ||
           (filter.category?.isNotEmpty ?? false) ||
           (filter.cattleId?.isNotEmpty ?? false) ||
           filter.fromDate != null ||
           filter.toDate != null ||
           (filter.searchQuery?.isNotEmpty ?? false);
  }

  /// Mark notification as read
  Future<void> markAsRead(String businessId) async {
    try {
      await _notificationRepository.markAsRead(businessId);
      // Stream will automatically update the UI
    } catch (e, stackTrace) {
      debugPrint('Error marking notification as read: $e\n$stackTrace');
      throw Exception('Failed to mark notification as read: ${e.toString()}');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String businessId) async {
    try {
      await _notificationRepository.deleteNotificationByBusinessId(businessId);
      // Stream will automatically update the UI
    } catch (e, stackTrace) {
      debugPrint('Error deleting notification: $e\n$stackTrace');
      throw Exception('Failed to delete notification: ${e.toString()}');
    }
  }

  /// Refresh all data
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      _updateAnalytics();
      _applyFilters();
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing notifications data: $e\n$stackTrace');
      throw Exception('Failed to refresh notifications data: ${e.toString()}');
    }
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions to prevent memory leaks
    _unfilteredNotificationSubscription?.cancel();
    super.dispose();
  }
}
