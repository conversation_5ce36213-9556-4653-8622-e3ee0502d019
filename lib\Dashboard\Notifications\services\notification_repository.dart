import 'package:isar/isar.dart';
import '../../../services/database/isar_service.dart';
import '../models/notification_isar.dart';
import '../models/notification_filter.dart';
import '../models/notification_status.dart';

/// Pure reactive repository for Notifications module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class NotificationRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  NotificationRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE NOTIFICATIONS STREAMS ===//

  /// Watches all notifications with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<NotificationIsar>> watchAllNotifications() {
    return _isar.notificationIsars.where().watch(fireImmediately: true);
  }

  //=== NOTIFICATIONS CRUD ===//

  /// Save (add or update) a notification using <PERSON><PERSON>'s native upsert
  Future<void> saveNotification(NotificationIsar notification) async {
    await _isar.writeTxn(() async {
      await _isar.notificationIsars.put(notification);
    });
  }

  /// Delete a notification by its Isar ID
  Future<void> deleteNotification(int id) async {
    await _isar.writeTxn(() async {
      await _isar.notificationIsars.delete(id);
    });
  }

  /// Mark notification as read by business ID
  Future<void> markAsRead(String businessId) async {
    final notification = await _isar.notificationIsars.getByBusinessId(businessId);
    if (notification != null) {
      notification.status = NotificationStatus.read;
      notification.readAt = DateTime.now();
      notification.updatedAt = DateTime.now();
      await saveNotification(notification);
    }
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all notifications (for analytics and reports)
  /// Returns a Future<List> for one-time data fetching
  Future<List<NotificationIsar>> getAllNotifications() async {
    return await _isar.notificationIsars.where().findAll();
  }

  /// Get a notification by its business ID
  Future<NotificationIsar?> getNotificationById(String businessId) async {
    return await _isar.notificationIsars.getByBusinessId(businessId);
  }

  /// Get notifications with filtering (for controller use)
  Future<List<NotificationIsar>> getNotifications({NotificationFilter? filter}) async {
    if (filter == null) {
      return await getAllNotifications();
    }

    var query = _isar.notificationIsars.filter();

    if (filter.status != null) {
      query = query.statusEqualTo(filter.status!);
    }

    if (filter.category != null && filter.category!.isNotEmpty) {
      query = query.categoryEqualTo(filter.category!);
    }

    if (filter.cattleId != null && filter.cattleId!.isNotEmpty) {
      query = query.cattleIdEqualTo(filter.cattleId!);
    }

    if (filter.fromDate != null && filter.toDate != null) {
      query = query.createdAtBetween(filter.fromDate!, filter.toDate!);
    }

    return await query.findAll();
  }

  /// Get notifications by status for analytics
  Future<List<NotificationIsar>> getNotificationsByStatus(NotificationStatus status) async {
    return await _isar.notificationIsars
        .filter()
        .statusEqualTo(status)
        .findAll();
  }

  /// Get notifications by category for analytics
  Future<List<NotificationIsar>> getNotificationsByCategory(String category) async {
    return await _isar.notificationIsars
        .filter()
        .categoryEqualTo(category)
        .findAll();
  }

  /// Get notifications by date range for analytics
  Future<List<NotificationIsar>> getNotificationsByDateRange(DateTime startDate, DateTime endDate) async {
    return await _isar.notificationIsars
        .filter()
        .createdAtBetween(startDate, endDate)
        .findAll();
  }

  /// Get unread count for analytics
  Future<int> getUnreadCount() async {
    return await _isar.notificationIsars
        .filter()
        .statusEqualTo(NotificationStatus.unread)
        .count();
  }

  /// Delete notification by business ID
  Future<void> deleteNotificationByBusinessId(String businessId) async {
    final notification = await _isar.notificationIsars.getByBusinessId(businessId);
    if (notification != null) {
      await deleteNotification(notification.id);
    }
  }
}
