import 'dart:async';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';

import 'notification_repository.dart';
import 'notification_settings_repository.dart';
import 'push_notification_service.dart';
import '../models/notification_isar.dart';
import '../models/notification_settings_isar.dart';
import '../models/push_notification_request.dart';
import '../models/notification_priority.dart';

/// Focused service for notification delivery operations
/// Handles push notifications, email notifications, and delivery logic
/// Single responsibility: delivering notifications through various channels
class NotificationDeliveryService {
  final Logger _logger = Logger('NotificationDeliveryService');
  
  // Use lazy getters to avoid accessing GetIt services in constructor
  NotificationRepository get _repository => GetIt.instance<NotificationRepository>();
  NotificationSettingsRepository get _settingsRepository => GetIt.instance<NotificationSettingsRepository>();
  PushNotificationService get _pushService => GetIt.instance<PushNotificationService>();

  //=== DELIVERY METHODS ===//

  /// Send push notification for a saved notification
  Future<void> sendPushNotification(NotificationIsar notification) async {
    try {
      final settings = await _settingsRepository.getSettings();
      
      if (!settings.pushNotificationsEnabled) {
        _logger.info('Push notifications disabled - skipping notification ${notification.businessId}');
        return;
      }

      if (!_shouldSendPushNotification(notification.category, notification.priority, settings)) {
        _logger.info('Push notification filtered out for ${notification.businessId}');
        return;
      }

      // Check quiet hours
      if (_isInQuietHours(settings)) {
        _logger.info('In quiet hours - skipping push notification ${notification.businessId}');
        return;
      }

      // Create push notification request
      final pushRequest = PushNotificationRequest(
        title: notification.title ?? 'Cattle Manager',
        body: notification.message ?? '',
        data: {
          'notificationId': notification.businessId ?? '',
          'category': notification.category ?? '',
          'type': notification.type ?? '',
          'cattleId': notification.cattleId ?? '',
          'actionUrl': notification.actionUrl ?? '',
        },
        imageUrl: notification.imageUrl,
        actionUrl: notification.actionUrl,
      );

      // Send push notification
      final pushNotificationId = await _pushService.sendNotification(pushRequest);

      // Update notification with push details
      notification.pushNotificationId = pushNotificationId;
      notification.pushNotificationSent = true;
      notification.pushNotificationSentAt = DateTime.now();
      notification.updatedAt = DateTime.now();

      // Save updated notification
      await _repository.saveNotification(notification);

      _logger.info('Push notification sent for ${notification.businessId}');
    } catch (e) {
      _logger.severe('Error sending push notification for ${notification.businessId}: $e');
      rethrow;
    }
  }

  /// Send email notification (placeholder for future implementation)
  Future<void> sendEmailNotification(NotificationIsar notification) async {
    try {
      final settings = await _settingsRepository.getSettings();
      
      if (!settings.emailNotificationsEnabled) {
        _logger.info('Email notifications disabled - skipping notification ${notification.businessId}');
        return;
      }

      // TODO: Implement email delivery
      _logger.info('Email notification would be sent for ${notification.businessId}');
    } catch (e) {
      _logger.severe('Error sending email notification for ${notification.businessId}: $e');
      rethrow;
    }
  }

  /// Send SMS notification (placeholder for future implementation)
  Future<void> sendSmsNotification(NotificationIsar notification) async {
    try {
      final settings = await _settingsRepository.getSettings();
      
      if (!settings.smsNotificationsEnabled) {
        _logger.info('SMS notifications disabled - skipping notification ${notification.businessId}');
        return;
      }

      // TODO: Implement SMS delivery
      _logger.info('SMS notification would be sent for ${notification.businessId}');
    } catch (e) {
      _logger.severe('Error sending SMS notification for ${notification.businessId}: $e');
      rethrow;
    }
  }

  /// Deliver notification through all enabled channels
  Future<void> deliverNotification(NotificationIsar notification) async {
    try {
      final settings = await _settingsRepository.getSettings();

      // Send through enabled channels
      final deliveryTasks = <Future<void>>[];

      if (settings.pushNotificationsEnabled) {
        deliveryTasks.add(sendPushNotification(notification));
      }

      if (settings.emailNotificationsEnabled) {
        deliveryTasks.add(sendEmailNotification(notification));
      }

      if (settings.smsNotificationsEnabled) {
        deliveryTasks.add(sendSmsNotification(notification));
      }

      // Execute all delivery tasks concurrently
      await Future.wait(deliveryTasks);

      _logger.info('Notification delivered through all enabled channels: ${notification.businessId}');
    } catch (e) {
      _logger.severe('Error delivering notification ${notification.businessId}: $e');
      rethrow;
    }
  }

  //=== DELIVERY LOGIC HELPERS ===//

  /// Check if push notification should be sent based on category and priority
  bool _shouldSendPushNotification(
    String? category,
    NotificationPriority priority,
    NotificationSettingsIsar settings,
  ) {
    // Check if category is enabled
    if (category != null && !settings.isCategoryEnabled(category)) {
      return false;
    }

    // Always send high priority notifications
    if (priority == NotificationPriority.high || priority == NotificationPriority.urgent) {
      return true;
    }

    // For medium and low priority, respect category settings
    return category != null && settings.isCategoryEnabled(category);
  }

  /// Check if current time is within quiet hours
  bool _isInQuietHours(NotificationSettingsIsar settings) {
    if (!settings.quietHoursEnabled) {
      return false;
    }

    final now = DateTime.now();
    final currentTime = now.hour * 60 + now.minute; // Convert to minutes since midnight
    
    final startTime = _parseTimeString(settings.quietHoursStart);
    final endTime = _parseTimeString(settings.quietHoursEnd);

    if (startTime == null || endTime == null) {
      return false;
    }

    // Handle overnight quiet hours (e.g., 22:00 to 06:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      return currentTime >= startTime && currentTime <= endTime;
    }
  }

  /// Parse time string (HH:mm) to minutes since midnight
  int? _parseTimeString(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return null;
    }

    try {
      final parts = timeString.split(':');
      if (parts.length != 2) {
        return null;
      }

      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);

      if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        return null;
      }

      return hours * 60 + minutes;
    } catch (e) {
      _logger.warning('Error parsing time string "$timeString": $e');
      return null;
    }
  }

  /// Get delivery status for a notification
  Future<Map<String, bool>> getDeliveryStatus(String notificationId) async {
    try {
      final notification = await _repository.getNotificationById(notificationId);
      if (notification == null) {
        return {};
      }

      return {
        'pushSent': notification.pushNotificationSent,
        'emailSent': false, // TODO: Track email delivery
        'smsSent': false,   // TODO: Track SMS delivery
      };
    } catch (e) {
      _logger.severe('Error getting delivery status for $notificationId: $e');
      return {};
    }
  }

  /// Retry failed deliveries
  Future<void> retryFailedDeliveries() async {
    try {
      // TODO: Implement retry logic for failed deliveries
      _logger.info('Retry failed deliveries - not yet implemented');
    } catch (e) {
      _logger.severe('Error retrying failed deliveries: $e');
      rethrow;
    }
  }
}
