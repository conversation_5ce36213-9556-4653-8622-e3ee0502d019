import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';

import '../models/notification_isar.dart';
import '../models/notification_request.dart';
import '../models/scheduled_notification_request.dart';
import '../models/notification_priority.dart';

/// Focused service for notification template and content generation
/// Handles notification creation, templating, and content formatting
/// Single responsibility: creating and formatting notification content
class NotificationTemplateService {
  final Logger _logger = Logger('NotificationTemplateService');
  final Uuid _uuid = const Uuid();

  //=== NOTIFICATION CREATION ===//

  /// Create notification from request with proper templating
  NotificationIsar createNotificationFromRequest(NotificationRequest request) {
    try {
      // Apply templates and formatting
      final formattedTitle = _formatTitle(request.title, request.category);
      final formattedMessage = _formatMessage(request.message, request.category);
      
      return NotificationIsar(
        title: formattedTitle,
        message: formattedMessage,
        category: request.category,
        type: request.type,
        priority: request.priority,
        cattleId: request.cattleId,
        relatedRecordId: request.relatedRecordId,
        relatedRecordType: request.relatedRecordType,
        eventId: request.eventId,
        imageUrl: request.imageUrl,
        actionUrl: request.actionUrl,
        customData: request.customData,
        createdAt: DateTime.now(),
        businessId: _uuid.v4(),
        isSynced: false,
      );
    } catch (e) {
      _logger.severe('Error creating notification from request: $e');
      rethrow;
    }
  }

  /// Create scheduled notification from request
  NotificationIsar createScheduledNotificationFromRequest(ScheduledNotificationRequest request) {
    try {
      // Apply templates and formatting
      final formattedTitle = _formatTitle(request.title, request.category);
      final formattedMessage = _formatMessage(request.message, request.category);
      
      return NotificationIsar(
        title: formattedTitle,
        message: formattedMessage,
        category: request.category,
        type: request.type,
        priority: request.priority,
        cattleId: request.cattleId,
        relatedRecordId: request.relatedRecordId,
        relatedRecordType: request.relatedRecordType,
        eventId: request.eventId,
        imageUrl: request.imageUrl,
        actionUrl: request.actionUrl,
        customData: request.customData,
        scheduledFor: request.scheduledFor,
        expiresAt: request.expiresAt,
        isRecurring: request.isRecurring,
        recurringPattern: request.recurringPattern,
        createdAt: DateTime.now(),
        businessId: _uuid.v4(),
      );
    } catch (e) {
      _logger.severe('Error creating scheduled notification from request: $e');
      rethrow;
    }
  }

  //=== TEMPLATE FORMATTING ===//

  /// Format notification title based on category
  String _formatTitle(String title, String? category) {
    if (title.trim().isEmpty) {
      return _getDefaultTitle(category);
    }

    // Apply category-specific formatting
    switch (category?.toLowerCase()) {
      case 'health':
        return '🏥 $title';
      case 'breeding':
        return '💕 $title';
      case 'milk':
        return '🥛 $title';
      case 'weight':
        return '⚖️ $title';
      case 'events':
        return '📅 $title';
      case 'system':
        return '⚙️ $title';
      case 'alert':
        return '⚠️ $title';
      case 'reminder':
        return '🔔 $title';
      default:
        return title;
    }
  }

  /// Format notification message based on category
  String _formatMessage(String message, String? category) {
    if (message.trim().isEmpty) {
      return _getDefaultMessage(category);
    }

    // Apply category-specific message formatting
    switch (category?.toLowerCase()) {
      case 'health':
        return _formatHealthMessage(message);
      case 'breeding':
        return _formatBreedingMessage(message);
      case 'milk':
        return _formatMilkMessage(message);
      case 'weight':
        return _formatWeightMessage(message);
      case 'events':
        return _formatEventMessage(message);
      default:
        return message;
    }
  }

  /// Get default title for category
  String _getDefaultTitle(String? category) {
    switch (category?.toLowerCase()) {
      case 'health':
        return '🏥 Health Notification';
      case 'breeding':
        return '💕 Breeding Notification';
      case 'milk':
        return '🥛 Milk Production Notification';
      case 'weight':
        return '⚖️ Weight Notification';
      case 'events':
        return '📅 Event Notification';
      case 'system':
        return '⚙️ System Notification';
      case 'alert':
        return '⚠️ Alert';
      case 'reminder':
        return '🔔 Reminder';
      default:
        return 'Cattle Manager Notification';
    }
  }

  /// Get default message for category
  String _getDefaultMessage(String? category) {
    switch (category?.toLowerCase()) {
      case 'health':
        return 'A health-related notification requires your attention.';
      case 'breeding':
        return 'A breeding-related notification requires your attention.';
      case 'milk':
        return 'A milk production notification requires your attention.';
      case 'weight':
        return 'A weight-related notification requires your attention.';
      case 'events':
        return 'An event notification requires your attention.';
      case 'system':
        return 'A system notification requires your attention.';
      default:
        return 'A notification requires your attention.';
    }
  }

  //=== CATEGORY-SPECIFIC FORMATTING ===//

  String _formatHealthMessage(String message) {
    // Add health-specific formatting
    if (!message.contains('Health:')) {
      return 'Health: $message';
    }
    return message;
  }

  String _formatBreedingMessage(String message) {
    // Add breeding-specific formatting
    if (!message.contains('Breeding:')) {
      return 'Breeding: $message';
    }
    return message;
  }

  String _formatMilkMessage(String message) {
    // Add milk-specific formatting
    if (!message.contains('Milk:')) {
      return 'Milk: $message';
    }
    return message;
  }

  String _formatWeightMessage(String message) {
    // Add weight-specific formatting
    if (!message.contains('Weight:')) {
      return 'Weight: $message';
    }
    return message;
  }

  String _formatEventMessage(String message) {
    // Add event-specific formatting
    if (!message.contains('Event:')) {
      return 'Event: $message';
    }
    return message;
  }

  //=== TEMPLATE UTILITIES ===//

  /// Generate notification content for cattle health alerts
  NotificationRequest createHealthAlertTemplate({
    required String cattleId,
    required String condition,
    required String severity,
  }) {
    return NotificationRequest(
      title: 'Health Alert: $condition',
      message: 'Cattle $cattleId requires attention for $condition (Severity: $severity)',
      category: 'health',
      type: 'alert',
      priority: _getPriorityFromSeverity(severity),
      cattleId: cattleId,
      relatedRecordType: 'health_record',
    );
  }

  /// Generate notification content for breeding reminders
  NotificationRequest createBreedingReminderTemplate({
    required String cattleId,
    required String eventType,
    required DateTime dueDate,
  }) {
    final daysUntilDue = dueDate.difference(DateTime.now()).inDays;
    
    return NotificationRequest(
      title: 'Breeding Reminder: $eventType',
      message: 'Cattle $cattleId has $eventType due in $daysUntilDue days',
      category: 'breeding',
      type: 'reminder',
      priority: daysUntilDue <= 1 ? NotificationPriority.high : NotificationPriority.medium,
      cattleId: cattleId,
      relatedRecordType: 'breeding_record',
    );
  }

  /// Generate notification content for milk production alerts
  NotificationRequest createMilkProductionTemplate({
    required String cattleId,
    required double currentProduction,
    required double expectedProduction,
  }) {
    final percentageChange = ((currentProduction - expectedProduction) / expectedProduction * 100).round();
    
    return NotificationRequest(
      title: 'Milk Production Alert',
      message: 'Cattle $cattleId production is ${percentageChange > 0 ? '+' : ''}$percentageChange% from expected',
      category: 'milk',
      type: percentageChange < -20 ? 'alert' : 'info',
      priority: percentageChange < -20 ? NotificationPriority.high : NotificationPriority.medium,
      cattleId: cattleId,
      relatedRecordType: 'milk_record',
    );
  }

  /// Get priority from severity string
  NotificationPriority _getPriorityFromSeverity(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
      case 'urgent':
        return NotificationPriority.urgent;
      case 'high':
        return NotificationPriority.high;
      case 'medium':
        return NotificationPriority.medium;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.medium;
    }
  }
}
